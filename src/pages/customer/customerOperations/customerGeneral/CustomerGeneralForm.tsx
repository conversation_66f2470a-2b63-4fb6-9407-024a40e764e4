import {
  Button,
  Divider,
  Form,
  Input,
  InputRef,
  Select,
  Space,
  Switch,
  Tooltip,
  Typography,
} from 'antd';
import './CustomerGeneralForm.css';
import { infoCircleOutlined } from '@/assets';
import { formErrorRegex } from '@/constant/Regex';
import {
  numberFieldValidator,
  validateCountryAndValue,
  validateMaskedInput,
} from '@/lib/FormValidators';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { MaskedInput } from 'antd-mask-input';
import { MaskType } from 'antd-mask-input/build/main/lib/MaskedInput';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from '@/constant/RoutesConstant';
import { useNotificationManager } from '@/hooks/useNotificationManger';
import { useLanguage } from '@/hooks/useLanguage';
import { customAlert } from '@/components/common/customAlert/CustomAlert';
import CustomTooltip from '@/components/common/customTooltip/CustomTooltip';
import { Autocomplete } from '@react-google-maps/api';
import { googlePlaceDataMasking } from '@/lib/GooglePlace';
import { customerHook, customerService } from '@/api/customer/useCustomer';
import { CreateCustomerDto, ICustomer } from '@/api/customer/customer.types';
import { customerCategoryHook } from '@/api/customer/useCustomerCategories';
import { CustomerCategories } from '@/api/customer/customerCategories.types';
import { useGooglePlaceDropdownFix } from '@/hooks/useGooglePlaceDropdownFix';
import usePreventExits from '@/hooks/usePreventExits';
import { useNavigationContext } from '@/hooks/useNavigationContext';
import { isFormChangedHandler } from '@/lib/helper';
import { optionsForPrefix } from '@/constant/CountryCodeConstant';
import CustomGoogleAutoComplete from '@/components/common/customGoogleAutoComplete/CustomGoogleAutoComplete';

const websitePrefix = (
  <Form.Item className=" mb-0" name={'websitePrefix'}>
    <Typography.Text className="!text-black" type="secondary" defaultValue={'https://'}>
      https://
    </Typography.Text>
  </Form.Item>
);
interface ValuesObject {
  value: string;
  label: string;
}
type ICustomerStatus = 'Active' | 'Inactive';
const CustomerGeneralFormComponent = () => {
  const { t } = useLanguage();

  const { id } = useParams<{ id: string }>();
  const [maskFaxInput, setMaskFaxInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [maskPhoneInput, setMaskPhoneInput] = useState<{
    label: string;
    value: string;
    mask: MaskType;
    length: number;
  }>(optionsForPrefix[0]);
  const [isCustomerActive, setIsCustomerActive] = useState<ICustomerStatus>();
  const [customerGeneralForm] = Form.useForm();
  const InitialValue = useMemo(
    () => customerGeneralForm.getFieldsValue(true),
    [customerGeneralForm]
  );
  const { setPreventExit } = usePreventExits();
  const { setIsBlocked } = useNavigationContext();

  const [searchResult, setSearchResult] = useState<google.maps.places.Autocomplete | null>();
  const [selectedCategories, setSelectedCategories] = useState<ValuesObject[]>([]);
  const [customerCategories, setCustomerCategories] = useState<ValuesObject[]>([]);
  const navigate = useNavigate();
  const [formData, setFormData] = useState<ICustomer>();
  const notificationManager = useNotificationManager();
  const autocompleteRef = useRef<Autocomplete>(null);
  const isEditMode = Boolean(id);
  const { data: customerCategoriesList } = customerCategoryHook.useEntity('CUSTOMER', {
    enabled: isEditMode,
  });
  const { data: customerList } = customerHook.useEntity(id as string, {
    enabled: isEditMode,
  });
  const { refetch: refetchCustomerList } = customerHook.useList({ pageNumber: 1, pageSize: 100 });
  const getCurrentCustomer = useCallback(async () => {
    if (id && customerList) {
      const categoriesFormat = customerList?.categories?.map((item) => item?.id);
      setFormData({
        ...customerList,
        categories: categoriesFormat as unknown as CustomerCategories[],
      });
    }
  }, [id, customerList]);
  useEffect(() => {
    if (id) {
      getCurrentCustomer();
    }
  }, [getCurrentCustomer, id]);
  useEffect(() => {
    if (customerCategoriesList) {
      const formattedCategoriesList = customerCategoriesList?.map((item) => {
        return {
          label: item?.name,
          value: item?.id,
          id: item?.id,
        };
      });
      setCustomerCategories(formattedCategoriesList);
    }
  }, [customerCategoriesList]);
  useEffect(() => {
    getCurrentCoordinates();
    if (!id) {
      const getAccountNumber = async () => {
        const response = await customerService.generateAccountNumber();
        customerGeneralForm.setFieldValue('accountNumber', response.accountNumber);
      };
      getAccountNumber();
    }
  }, []);
  function getCurrentCoordinates() {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition((position) => {
        const { latitude, longitude } = position.coords;
        getCountryFromCoordinates(latitude, longitude);
      });
    }
  }
  useGooglePlaceDropdownFix(true, 'autoComplete');

  const maskingInput = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    if (focus) {
      inputFaxRef.current?.focus();
      setMaskFaxInput(selectedCountryMask);
    }
  }, []);
  const maskingInputPhone = useCallback((value: string, focus = true) => {
    const selectedCountryMask = optionsForPrefix?.find((item) => item.value === value);
    if (!selectedCountryMask) return;
    if (focus) {
      inputPhoneRef.current?.focus();
      setMaskPhoneInput(selectedCountryMask);
    }
  }, []);
  const getCountryFromCoordinates = async (latitude: number, longitude: number) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10`
      );
      const data = await response.json();

      if (data && data.address) {
        const country = data.address.country;
        optionsForPrefix?.find((item) => {
          const countryMatches = item.geoCountryCode === country;
          if (countryMatches && !id) {
            customerGeneralForm.setFieldValue('phoneCountryCode', item.label);
            customerGeneralForm.setFieldValue('faxNumberCountryCode', item.label);
            maskingInput(item.value);
            maskingInputPhone(item.value);
          }
        });
      }
    } catch (error) {
      console.error(t('dashboard.customer.columns.errorFetchingCountry'), error);
    }
  };
  const handleCustomerActive = (checked: boolean) => {
    if (!id) {
      setIsCustomerActive(checked ? 'Active' : 'Inactive');
      customerGeneralForm.setFieldValue('status', checked);
    } else {
      if (checked === true) {
        customAlert.warning({
          title: t('dashboard.customer.columns.confirmEnableCustomer'),
          message: t('dashboard.customer.columns.enableCustomerDescription'),
          firstButtonTitle: t('common.cancel'),
          secondButtonTitle: t('dashboard.customer.columns.activate'),
          firstButtonFunction: () => {
            customAlert.destroy();
          },

          secondButtonFunction: () => {
            setIsCustomerActive('Active');
            customerGeneralForm.setFieldValue('status', checked);
            customAlert.destroy();
          },
        });
      } else {
        customAlert.error({
          title: t('dashboard.customer.columns.confirmDisableCustomer'),
          message: t('dashboard.customer.columns.disableCustomerDescription'),
          firstButtonTitle: t('dashboard.customer.columns.deactivate'),
          secondButtonTitle: t('common.cancel'),
          firstButtonFunction: () => {
            setIsCustomerActive('Inactive');
            customerGeneralForm.setFieldValue('status', checked);
            customAlert.destroy();
          },
          secondButtonFunction: async () => {
            customAlert.destroy();
          },
        });
      }
    }
  };

  useEffect(() => {
    setIsCustomerActive('Active');
    customerGeneralForm.setFieldValue('status', 'Active');
  }, []);
  useEffect(() => {
    customerGeneralForm.resetFields();
    customerGeneralForm.setFieldsValue({
      phone: formData?.phoneNumber,
      fax: formData?.faxNumber,
    });
  }, [customerGeneralForm, formData?.faxNumber, formData?.phoneNumber]);
  useEffect(() => {
    if (formData) {
      customerGeneralForm.setFieldsValue({
        ...formData,
        phone: formData.phoneNumber,
        fax: formData.faxNumber,
        phoneCountryCode: formData.phoneNumberCountryCode,
      });
      customerGeneralForm.setFieldValue('categories', formData.categories);
      maskingInput(formData.faxCountryCode);
      maskingInputPhone(formData.phoneNumberCountryCode);
      setIsCustomerActive(formData?.status as ICustomerStatus);
    }
  }, [customerGeneralForm, formData, maskingInput, maskingInputPhone]);
  const updateCustomerMutation = customerHook.useUpdate({
    onSuccess: () => {
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.columns.customerUpdatedSuccessfully'),
      });
    },
  });
  const createCustomerMutation = customerHook.useCreate({
    onSuccess: async () => {
      await refetchCustomerList();
      notificationManager.success({
        message: t('common.success'),
        description: t('dashboard.customer.columns.customerCreatedSuccessfully'),
      });
    },
  });

  const formValues = async () => {
    const values = customerGeneralForm.getFieldsValue();
    if (id) {
      const data = {
        ...values,
        categories: selectedCategories,
        status: values.status ? 'Active' : 'Inactive',
        faxNumber: values.fax as string,
        faxCountryCode: values.faxNumberCountryCode as string,
        phoneNumber: values.phone as string,
      };
      await updateCustomerMutation.mutateAsync({ id: id, data: data });
    } else {
      const dataToSend = {
        companyName: values.companyName as string,
        contactName: values.contactName as string,
        accountNumber: values.accountNumber as string,
        email: values.email as string,
        addressLine1: values.addressLine1 as string,
        addressLine2: values.addressLine2 as string,
        city: values.city as string,
        postalCode: values.postalCode as string,
        faxNumber: values.fax as string,
        province: values.province as string,
        country: values.country as string,
        phoneCountryCode: values.phoneCountryCode as string,
        phoneNumber: values.phone as string,
        status: values.status ? 'Active' : 'Inactive',
        categories: selectedCategories,
        phoneExtension: values.phoneExtension as string,
        website: values.website as string,
        faxCountryCode: values.faxNumberCountryCode as string,
      };
      const response = await createCustomerMutation.mutateAsync(
        dataToSend as unknown as CreateCustomerDto
      );
      navigate(
        ROUTES.CUSTOMER.CUSTOMER_TAB.replace(':id', response.id).replace(':tab', 'general'),
        {
          replace: true,
        }
      );
    }
  };
  const inputFaxRef = useRef<InputRef>(null);
  const inputPhoneRef = useRef<InputRef>(null);
  const onLoad = useCallback((autocomplete: google.maps.places.Autocomplete | null | undefined) => {
    setSearchResult(autocomplete);
  }, []);

  const onPlaceChanged = useCallback(() => {
    if (searchResult != null) {
      const place = searchResult.getPlace();
      const selectedPlaceData = googlePlaceDataMasking(place);
      const newFormValues = {
        addressLine1: selectedPlaceData?.addressLine1,
        city: selectedPlaceData?.city,
        postalCode: selectedPlaceData?.postalCode,
        province: selectedPlaceData?.state,
        country: selectedPlaceData?.country,
      };
      customerGeneralForm.setFieldsValue(newFormValues);
    }
  }, [customerGeneralForm, searchResult]);

  useEffect(() => {
    maskingInputPhone(customerGeneralForm.getFieldValue('phoneCountryCode'), false);
  }, [customerGeneralForm, maskPhoneInput, maskingInputPhone]);
  const handleSendCredentials = () => {
    notificationManager.success({
      message: t('common.success'),
      description: t('common.alert.linkSent', {
        email: customerGeneralForm.getFieldValue('email'),
      }),
    });
  };
  const phoneNumberPrefix = (
    <Form.Item className="general-form-item-prefix" name={'phoneCountryCode'}>
      <Select
        placeholder="USA +1"
        options={optionsForPrefix}
        onChange={(value) => maskingInputPhone(value)}
      />
    </Form.Item>
  );
  const faxNumberPrefix = (
    <Form.Item className="general-form-item-prefix" name={'faxNumberCountryCode'}>
      <Select
        placeholder="USA +1"
        options={optionsForPrefix}
        onChange={(value) => maskingInput(value)}
      />
    </Form.Item>
  );
  const handleCategoryOnChange = (value: (string | ValuesObject)[], values: ValuesObject[]) => {
    const formattedCategories = value.map((category) => {
      if (typeof category === 'string') {
        const existingCategory = values.find(
          (item) => item.value === category || item.label === category
        );
        if (existingCategory) {
          return { id: existingCategory.value, name: existingCategory.label };
        } else {
          return { name: category };
        }
      } else {
        return category;
      }
    });
    setSelectedCategories(formattedCategories as ValuesObject[]);
  };
  return (
    <div className="flex justify-center w-full">
      <Form
        layout="vertical"
        onFinish={() => {
          formValues();
        }}
        className="customer-general-form"
        form={customerGeneralForm}
        onFieldsChange={(changesFields) => {
          if (changesFields.length <= 1) {
            const isIsChange = isFormChangedHandler(
              InitialValue,
              customerGeneralForm.getFieldsValue(true),
              ['customerGeneral', '']
            );
            setPreventExit(isIsChange);
          } else if (changesFields.length > 1) {
            setIsBlocked(false);
            setPreventExit(false);
          }
        }}
      >
        <Divider
          plain
          className="customer-general-divider"
          orientation="left"
          orientationMargin={0}
        >
          {t('zonePage.basicDetails')}
        </Divider>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourName') },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              pattern: formErrorRegex.NoSpecialCharacters,
              message: t('common.errors.noSpacialCharacters'),
            },
          ]}
          labelAlign="left"
          className="customer-general-form-item"
          name={'companyName'}
          label={t('dashboard.customer.columns.formFieldNames.companyName')}
        >
          <Input
            min={3}
            maxLength={255}
            className="customer-general-input"
            placeholder="Jhon doe"
          />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourName') },

            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              pattern: formErrorRegex.NoSpecialCharacters,
              message: t('common.errors.noSpacialCharacters'),
            },
          ]}
          className="customer-general-form-item"
          name={'contactName'}
          label={t('dashboard.customer.columns.formFieldNames.contactName')}
        >
          <Input
            min={3}
            maxLength={255}
            className="customer-general-input"
            placeholder="Jhon smith"
          />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourName') },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              pattern: formErrorRegex.NoSpecialCharacters,
              message: t('common.errors.noSpacialCharacters'),
            },
          ]}
          className="customer-general-form-item"
          name={'accountNumber'}
          label={t('dashboard.customer.columns.formFieldNames.accountNumber')}
        >
          <Input
            disabled
            min={5}
            maxLength={20}
            className="customer-general-input"
            placeholder="ACC12345"
          />
        </Form.Item>
        <Space.Compact className="combined-masked-input customer-general-form-maskedItem">
          <Form.Item
            className="customer-general-form-maskedItem !mb-[0px] w-[75%]"
            dependencies={['phoneCountryCode']}
            validateFirst
            rules={[
              {
                required: true,
                validator: validateCountryAndValue(
                  customerGeneralForm,
                  'phoneCountryCode',
                  'phone number',
                  true
                ),
              },
              {
                validator: (_, value) =>
                  validateMaskedInput(
                    value,
                    maskPhoneInput.length,
                    t('customerAddressPage.operationalForm.validPhoneNumberError')
                  ),
              },
            ]}
            name="phone"
            label={t('customerAddressPage.operationalForm.phoneNumber')}
          >
            <MaskedInput
              ref={inputPhoneRef}
              addonBefore={phoneNumberPrefix}
              className="customer-general-maskedInput address-popup-maskedInput"
              placeholder={t('customerAddressPage.operationalForm.phoneNumberPlaceholder')}
              mask={maskPhoneInput.mask}
              onChange={(event) => customerGeneralForm.setFieldValue('phone', event?.unmaskedValue)}
            />
          </Form.Item>
          <Form.Item name="phoneExtension" className="w-[25%] !mb-0">
            <Input
              onKeyDown={(event) => numberFieldValidator(event, {})}
              placeholder="00000"
              maxLength={6}
              className="customer-general-input mt-[30px]"
            />
          </Form.Item>
        </Space.Compact>
        <Form.Item
          dependencies={['faxNumberCountryCode']}
          className="customer-general-form-maskedItem !mb-[0px]"
          name="fax"
          label={t('dashboard.customer.columns.faxNumber')}
          rules={[
            {
              validator: (_, value) => {
                if (!value && !customerGeneralForm.getFieldValue('faxNumberCountryCode')) {
                  return Promise.resolve();
                }
                if (!value && customerGeneralForm.getFieldValue('faxNumberCountryCode')) {
                  return Promise.reject('Please enter your fax number');
                }
                if (value && !customerGeneralForm.getFieldValue('faxNumberCountryCode')) {
                  return Promise.reject('Please select your country');
                }
                if (value && customerGeneralForm.getFieldValue('faxNumberCountryCode')) {
                  return validateMaskedInput(
                    value,
                    maskFaxInput.length,
                    t('customerAddressPage.operationalForm.enterValidFaxNumber')
                  );
                }
              },
            },
          ]}
        >
          <MaskedInput
            ref={inputFaxRef}
            addonBefore={faxNumberPrefix}
            className="customer-general-maskedInput"
            placeholder="(123) 456-7890"
            mask={maskFaxInput.mask}
          />
        </Form.Item>
        <Form.Item
          rules={[
            {
              required: true,
              message: t('auth.emailRequired'),
            },
            {
              pattern: formErrorRegex.ValidEmailOrNot,
              message: `${t('auth.invalidEmail')}`,
            },
          ]}
          className="customer-general-form-item"
          name={'email'}
          label={t('dashboard.customer.columns.email')}
        >
          <Input
            maxLength={255}
            className="customer-general-input"
            placeholder="<EMAIL>"
          />
        </Form.Item>
        <Form.Item
          rules={[
            {
              pattern: formErrorRegex.CheckIfValidURL,
              message: t('dashboard.customer.columns.pleaseEnterValidURL'),
            },
          ]}
          className="customer-general-form-item"
          name={'website'}
          label={t('dashboard.customer.columns.website')}
        >
          <Input
            min={5}
            maxLength={255}
            className="customer-general-input"
            addonBefore={websitePrefix}
            placeholder="www.example.com"
            allowClear
          />
        </Form.Item>
        <Form.Item
          className="customer-general-form-item"
          name={'categories'}
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },

            {
              validator: (_, value) => {
                if (!value || value.length === 0) {
                  return Promise.resolve();
                }
                const invalidTags = value?.filter((tag: string) => tag.length > 255);
                if (invalidTags.length > 0) {
                  return Promise.reject(
                    new Error(t('priceModifiers.maximumValueExceeded', { max: 255 }))
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
          label={
            <span className="flex gap-1">
              {t('dashboard.customer.columns.category')}
              <Tooltip title={t('dashboard.customer.columns.selectAppropriateCategory')}>
                <img src={infoCircleOutlined} alt="info" />
              </Tooltip>
            </span>
          }
        >
          <Select
            maxTagTextLength={10}
            mode="tags"
            prefixCls="custom-select"
            placeholder={t('dashboard.customer.columns.selectAppropriateCategory')}
            style={{ width: '100%' }}
            tokenSeparators={[',']}
            options={customerCategories}
            onChange={(value, valueString) =>
              handleCategoryOnChange(value, valueString as ValuesObject[])
            }
            allowClear
          />
        </Form.Item>
        <Divider
          plain
          className="customer-general-divider"
          orientation="left"
          orientationMargin={0}
        >
          {t('dashboard.customer.columns.addressDetails')}
        </Divider>
        <CustomGoogleAutoComplete
          className="customer-general-form-item"
          onLoad={onLoad}
          onPlaceChanged={onPlaceChanged}
          ref={autocompleteRef}
        >
          <Form.Item
            rules={[
              { required: true, message: t('dashboard.customer.columns.pleaseEnterYourAddress') },
              {
                pattern: formErrorRegex.NoMultipleWhiteSpaces,
                message: t('common.errors.noMultipleWhiteSpace'),
              },
            ]}
            labelAlign="left"
            className="customer-general-form-item !w-full"
            name={'addressLine1'}
            label={t('dashboard.customer.columns.formFieldNames.addressLine1')}
          >
            <Input
              min={10}
              maxLength={255}
              className="customer-general-input"
              placeholder="123 Main St"
              id="autoComplete"
            />
          </Form.Item>
        </CustomGoogleAutoComplete>
        <Form.Item
          className="customer-general-form-item"
          name={'addressLine2'}
          label={t('dashboard.customer.columns.formFieldNames.addressLine2')}
          rules={[
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
        >
          <Input
            min={10}
            maxLength={255}
            className="customer-general-input"
            placeholder="Suite 100"
          />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourCity') },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
          className="customer-general-form-item"
          name={'city'}
          label={t('addressPage.colDefs.city')}
        >
          <Input
            min={2}
            maxLength={100}
            className="customer-general-input"
            placeholder="Montreal"
          />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourProvince') },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
          className="customer-general-form-item"
          name={'province'}
          label={t('addressPage.colDefs.province')}
        >
          <Input min={2} maxLength={100} className="customer-general-input" placeholder="Quebec" />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourPostalCode') },
            {
              pattern: formErrorRegex.PostalCode,
              message: t('addressPage.operationalForm.validPostalCodeError'),
            },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
          ]}
          className="customer-general-form-item"
          name={'postalCode'}
          label={t('customerAddressPage.colDefs.postalCode')}
        >
          <Input min={2} maxLength={20} className="customer-general-input" placeholder="H3Z 3Y7" />
        </Form.Item>
        <Form.Item
          rules={[
            { required: true, message: t('dashboard.customer.columns.pleaseEnterYourCountry') },
            {
              pattern: formErrorRegex.NoMultipleWhiteSpaces,
              message: t('common.errors.noMultipleWhiteSpace'),
            },
            {
              pattern: formErrorRegex.NoSpecialCharacters,
              message: t('common.errors.noSpacialCharacters'),
            },
          ]}
          className="customer-general-form-item"
          name={'country'}
          label={t('addressPage.operationalForm.country')}
        >
          <Input min={2} maxLength={100} className="customer-general-input" placeholder="Canada" />
        </Form.Item>
        <Form.Item
          name="status"
          layout="horizontal"
          initialValue={true}
          className="customer-active-key-item"
          label={
            <span className="flex gap-2 text-sm font-[500]">
              {t('dashboard.customer.columns.customerIsActive')}
              <CustomTooltip
                content={
                  <div className="flex flex-col gap-3 max-w-[350px]">
                    <span>{t('dashboard.customer.columns.toggleCustomerStatus')}</span>
                    <div>
                      <span className="font-semibold text-primary-600">
                        {t('dashboard.customer.columns.enabled')}:
                      </span>
                      <span>{t('dashboard.customer.columns.enabledDescription')}</span>
                    </div>
                    <div>
                      <span className="font-semibold text-primary-600">
                        {t('dashboard.customer.columns.disabled')}:
                      </span>
                      <span>{t('dashboard.customer.columns.disabledDescription')}</span>
                    </div>
                    <span className="font-semibold">
                      {t('dashboard.customer.columns.activeOrdersNote')}
                    </span>
                  </div>
                }
              >
                <img src={infoCircleOutlined} alt="info" />
              </CustomTooltip>
            </span>
          }
        >
          <Switch
            checked={isCustomerActive === 'Active' ? true : false}
            className="customer-general-switch"
            onChange={handleCustomerActive}
          />{' '}
        </Form.Item>
        <div className="flex gap-4">
          <Form.Item>
            <Button htmlType="submit" className="customer-general-form-save">
              {id ? t('common.update') : t('common.save')}
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              onClick={handleSendCredentials}
              disabled={!id}
              htmlType="button"
              className="h-[40px] hover:!text-black hover:!border-gray-400 border-gray-400"
            >
              {t('dashboard.customer.columns.sendCredentials')}
            </Button>
          </Form.Item>
        </div>
      </Form>
    </div>
  );
};

export default CustomerGeneralFormComponent;
